# -*- coding: utf-8 -*-
from flask import Blueprint, request, jsonify
from flask_jwt_extended import jwt_required, get_jwt_identity
from app import db
from app.models import User

user_bp = Blueprint('user', __name__)

@user_bp.route('/profile', methods=['GET'])
@jwt_required()
def get_profile():
    """获取用户资料"""
    user_id = get_jwt_identity()
    user = db.session.get(User, user_id)

    if not user:
        return jsonify({'error': '用户不存在'}), 404

    return jsonify(user.to_dict())

@user_bp.route('/profile', methods=['PUT'])
@jwt_required()
def update_profile():
    """更新用户资料"""
    user_id = get_jwt_identity()
    user = db.session.get(User, user_id)
    
    if not user:
        return jsonify({'error': '用户不存在'}), 404
    
    data = request.get_json()
    if not data:
        return jsonify({'error': '请求数据不能为空'}), 400
    
    # 可更新的字段
    updatable_fields = ['username', 'email', 'phone', 'avatar_url']

    for field in updatable_fields:
        if field in data:
            # 检查用户名唯一性
            if field == 'username' and data[field] != user.username:
                existing_user = User.query.filter_by(username=data[field]).first()
                if existing_user:
                    return jsonify({'error': '用户名已存在'}), 400

            # 检查邮箱唯一性
            if field == 'email' and data[field] != user.email:
                existing_user = User.query.filter_by(email=data[field]).first()
                if existing_user:
                    return jsonify({'error': '邮箱已被使用'}), 400

            # 检查手机号唯一性
            if field == 'phone' and data[field] != user.phone:
                existing_user = User.query.filter_by(phone=data[field]).first()
                if existing_user:
                    return jsonify({'error': '手机号已被使用'}), 400

            setattr(user, field, data[field])
    
    try:
        db.session.commit()
        return jsonify({
            'msg': '资料更新成功',
            'user': user.to_dict()
        })
    except Exception as e:
        db.session.rollback()
        return jsonify({'error': '更新失败'}), 500

@user_bp.route('/profile', methods=['PATCH'])
@jwt_required()
def patch_profile():
    """部分更新用户资料"""
    return update_profile()  # 复用PUT方法的逻辑
